// 用户角色常量
export const USER_ROLE = Object.freeze({
    TEMP_USER: 0,        // 临时用户
    NORMAL_USER: 1,      // 普通用户
    ADMIN: 2,            // 管理员
    SUPER_ADMIN: 3,      // 超级管理员
    DIRECTOR: 4,         // 主任
    0: 'TEMP_USER',
    1: 'NORMAL_USER',
    2: 'ADMIN',
    3: 'SUPER_ADMIN',
    4: 'DIRECTOR'
});

// 群组角色常量（对应 systemConfig.groupRole）
export const GROUP_ROLE = Object.freeze({
    NORMAL: 0,           // 普通成员
    MANAGER: 1,          // 管理员
    CREATOR: 2,          // 创建者
    0: 'NORMAL',
    1: 'MANAGER',
    2: 'CREATOR'
});

