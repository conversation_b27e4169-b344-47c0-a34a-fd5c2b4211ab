import {cloneDeep, groupBy ,uniqBy} from 'lodash'
import Vue from 'vue'
import permissionManager from '@/common/permission'

const initState ={
    // "1":{
    //     attendeeList:{'attendee_1':{}},
    //     chatMessageList:[],//聊天记录
    //     creator_id:1,
    //     id:1,
    //     is_public:1,
    //     record_mode:0,
    //     voice_ctrl_mode:0,
    //     mute_ctrl_mode:0,
    //     subject:'',
    //     is_single_chat:1,
    //     socket:{},
    //     type:1,
    //     video_list:[],
    //     rtc_voice_list:[],
    //     galleryObj:{
    //         gallery_list:[]addAttendee
    //     },
    //     is_loaded_history_list

    // }
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'conversationList',cloneDeep(initState))
            }
        },
        setConversation(state, valObj) {
            let cid=valObj.id+""
            if(!state[cid]){
                this.commit('conversationList/initConversation',cid)
            }
            if(!valObj.chatMessageList){
                valObj.chatMessageList=[]
            }

            valObj.galleryObj={
                gallery_list:[],
                gallery_index:0,
                image_list:[],
                image_index:0,
                video_list:[],
                video_index:0,
                exam_list:[],
                exam_index:0,
            }
            valObj.aiTaskInfo={};
            valObj.is_playing_video=false;
            valObj.joinedExamConsultations = {};
            valObj.is_loaded_history_list = false
            if(state[cid]&&state[cid].is_loaded_history_list){
                delete valObj.is_loaded_history_list
                delete valObj.chatMessageList
                delete valObj.galleryObj
            }
            if(state[cid]&&state[cid].aiTaskInfo){
                delete valObj.aiTaskInfo
            }
            valObj.iworksList = {
                index:0,
                list:[]
            };
            valObj.device_list=[];
            valObj.preferences ={
                is_mute:valObj.preferences&&valObj.preferences.is_mute||0
            }
            valObj.applyCount = 0;

            Object.keys(valObj).forEach((key)=>{
                Vue.set(state[cid],key,valObj[key]);
            })


            // window.mainDB.conversationList.put(cloneObj)

        },
        initConversation(state,cid){
            //初始化会话，在数据没返回前能进入聊天界面
            var defaultConversation={
                id:cid,
                attendeeList:{},
                subject:'',
                chatMessageList:[],
                is_playing_video:false,
                is_loaded_history_list:false,
                galleryObj:{
                    gallery_list:[],
                    gallery_index:0,
                    image_list:[],
                    image_index:0,
                    video_list:[],
                    video_index:0,
                    exam_list:[],
                    exam_index:0
                },
                joinedExamConsultations:{},
                conferencePlanList:{},
                preferences:{
                    is_mute:false
                },
                iworksList:{
                    index:0,
                    list:[]
                },
                aiTaskInfo:{},
                device_list:[],
                applyCount:0,
            }
            if(state.cid&&state.cid.chatMessageList){
                defaultConversation.chatMessageList = state.cid.chatMessageList
            }
            if(state.cid&&state.cid.aiTaskInfo){
                defaultConversation.aiTaskInfo = state.cid.aiTaskInfo
            }
            // state[cid]=defaultConversation
            Vue.set(state,cid,defaultConversation)
            // window.mainDB.conversationList.put(defaultConversation)
        },
        clearConversation(state){
            //清空所有会话
            console.error('clearConversation')
            for(let key in state){
                delete state[key]
            }
        },
        initGalleryObj(state,valObj){
            for(let item of valObj.gallery_list){
                item.loaded=false
            }
            if(valObj.gallery_list.length>0){
                valObj.gallery_index = valObj.gallery_index||valObj.gallery_list[valObj.gallery_list.length-1].resource_id
            }
            Vue.set(state[valObj.cid].galleryObj,'gallery_list',valObj.gallery_list)
            Vue.set(state[valObj.cid].galleryObj,'gallery_index',valObj.gallery_index)
        },
        addFileToConversation(state,data){
            //为会话文件列表添加文件
            let msg_type = window.vm.$store.state.systemConfig.msg_type;
            const imageType = [msg_type.Image, msg_type.Frame, msg_type.OBAI];
            const videoType = [msg_type.Video, msg_type.Cine, msg_type.RealTimeVideoReview, msg_type.VIDEO_CLIP];
            data.message.loaded=false
            data.message.realUrl=''
            // state[data.cid].galleryObj.total_count++
            let gallery_list=state[data.cid].galleryObj.gallery_list
            gallery_list.unshift(data.message)
            if(imageType.includes(data.message.msg_type)){
                let image_list=state[data.cid].galleryObj.image_list
                let image_index=state[data.cid].galleryObj.image_index
                image_index&&image_list.unshift(data.message)
            }else if(videoType.includes(data.message.msg_type)){
                let video_list=state[data.cid].galleryObj.video_list
                let video_index = state[data.cid].galleryObj.video_index
                video_index&&video_list.unshift(data.message)
            }
            if(data.message.hasOwnProperty('exam_id')){
                let exam_list=state[data.cid].galleryObj.exam_list
                let exam_index=state[data.cid].galleryObj.exam_index
                exam_index&&exam_list.unshift(data.message)
            }
        },
        deleteFileToConversation(state,data){
            let cid=data.cid;
            let resource_id=data.resource_id;
            if(resource_id && cid && state[cid]){
                let gallery_list=state[cid].galleryObj.gallery_list;
                // for(let i=gallery_list.length-1; i>=0; i--){
                //     if(resource_id==gallery_list[i].resource_id){
                //         gallery_list.splice(i,1);
                //     }
                // }
                let resourceIdList = []
                gallery_list.forEach(item=>{
                    if(!resourceIdList.includes(item.resource_id)){
                        if(item.resource_id !== null){
                            resourceIdList.push(item.resource_id)
                        }
                    }
                })
                const msgType = window.vm.$store.state.systemConfig.msg_type
                const resourceMsgTypeList = [msgType.Image,msgType.Video,msgType.COMMENT,msgType.TAG,msgType.Frame,msgType.EXAM_IMAGES]
                let chatMessageList=state[cid].chatMessageList;
                for(let i=chatMessageList.length-1; i>=0; i--){
                    let message=chatMessageList[i]
                    if (message.msg_type==msgType.EXAM_IMAGES) {
                        //判断聚合消息图像列表是否包含删除的图片,是则触发重新刷新
                        let resourceList=message.resourceList;
                        for(let index=0;index<resourceList.length;index++){
                            if (resource_id==resourceList[index].id) {
                                Vue.set(message,'imageList',null)
                                break;
                            }
                        }
                    }else if(message.hasOwnProperty('resource_id')&&!resourceIdList.includes(message.resource_id)&&resourceMsgTypeList.includes(message.msg_type)){
                        if(message.been_withdrawn!=1&&message.been_withdrawn!=2){
                            Vue.set(message,'msg_type',msgType.EXPIRATION_RES)
                            // window.mainDB.conversationMessageList.put(message)
                        }
                    }
                }
            }
        },
        setJoinedExamConsultations(state,data){
            let cid=data.cid;
            if(state[cid]){
                state[cid].joinedExamConsultations = data.list;
            }
        },
        setChatMessage(state,data){
            //将消息加入聊天记录
            let cid=data.cid
            if(!state[cid]){
                return
            }
            let is_localdb_msg=data.is_localdb_msg||0;
            let chatMessageList=state[cid].chatMessageList;
            let len=chatMessageList.length;
            for(let message of data.list){
                let type=data.type;
                let flag = 0;
                if(message.gmsg_id && message.gmsg_id != 0){
                    for(let chatMessage of state[cid].chatMessageList){
                        if(message.gmsg_id == chatMessage.gmsg_id){
                            flag = 1;
                            break;
                        }
                    }
                }
                if(flag == 1) {
                    console.log("ignore message %%%%%%%%%%")
                    continue;
                }
                if(state[cid] && state[cid].attendeeList&&state[cid].attendeeList['attendee_'+message.sender_id]){
                    if(!message.nickname){
                        message.nickname= state[cid].attendeeList['attendee_'+message.sender_id].nickname
                    }
                    message.avatar=state[cid].attendeeList['attendee_'+message.sender_id].avatar
                    message.avatar_local=state[cid].attendeeList['attendee_'+message.sender_id].avatar_local
                }
                message.is_localdb_msg = is_localdb_msg;
                if (message.origin_gmsg_id) {
                    //聚合消息更新
                    let isUpdate=false;
                    for (var i = chatMessageList.length - 1; i >= 0; i--) {
                        //从聊天记录尾部遍历，提高效率
                        let item=chatMessageList[i]
                        if (item.origin_gmsg_id==message.origin_gmsg_id||item.gmsg_id==message.origin_gmsg_id) {
                            if (item.gmsg_id>=message.gmsg_id) {
                                // 旧的聚合消息晚到达直接忽略
                                isUpdate=true
                                break;
                            }
                            chatMessageList.splice(i,1,message);
                            isUpdate=true
                            break;
                        }
                    }
                    if (isUpdate) {
                        continue;
                    }
                }
                if (type=='append') {
                    // 聚合视图如果收到第一条消息晚于其他聚合消息，则忽略聚合消息
                    let ignore = false;
                    for(let item of chatMessageList){
                        if (item.origin_gmsg_id&&item.origin_gmsg_id == message.gmsg_id) {
                            ignore = true;
                            break
                        }
                    }
                    if (ignore) {
                        continue;
                    }
                    state[cid].chatMessageList.push(message)
                    // window.mainDB.conversationMessageList.put(message)
                }else if(type=='prepend'){
                    state[cid].chatMessageList.unshift(message)
                    // window.mainDB.conversationMessageList.put(message)
                }else if(type=='splice'){
                    len--;
                    if(len >= 0){
                        //删除本地消息
                        flag = 0;
                        while(len >= 0){
                            let item=chatMessageList[len]
                            if((item.is_localdb_msg && item.is_localdb_msg == 1)||(item.gmsg_id==message.gmsg_id)){
                                chatMessageList.splice(len,1,message);
                                // window.mainDB.conversationMessageList.put(message)
                                flag = 1;
                                break;
                            }
                            //处理重连后发送失败的消息
                            if (item.gmsg_id==undefined) {
                                chatMessageList.splice(len,1);
                            }
                            if (!message.gmsg_id||message.gmsg_id==0) {
                                chatMessageList.push(message);
                                // window.mainDB.conversationMessageList.put(message)
                                flag = 1;
                                len++;
                                break;
                            }
                            len--;
                        }
                        if(flag == 0){
                            let lastMsg=state[cid].chatMessageList[chatMessageList.length-1]
                            if (lastMsg&&lastMsg.gmsg_id==message.gmsg_id) {
                                //未开启会话转发图片会导致say和history得到重复消息
                            }else{
                                state[cid].chatMessageList.unshift(message);
                                // window.mainDB.conversationMessageList.put(message)
                            }
                        }
                    }else{
                        state[cid].chatMessageList.unshift(message);
                        // window.mainDB.conversationMessageList.put(message)
                    }
                }
            }

        },
        deleteChatMessage(state,valObj){
            //删除某个消息
            let cid=valObj.cid
            state[cid].chatMessageList.splice(valObj.index,1)
        },
        deleteUploadChatMessage(state,{cid,file_id}){
            for(let i = state[cid].chatMessageList.length-1; i >= 0; i--){
                if(state[cid].chatMessageList[i].file_id===file_id){
                    state[cid].chatMessageList.splice(i,1)
                    break
                }
            }
        },
        deleteChatMessagesByGmsgIdList(state,valObj){
            //删除消息
            let cid=valObj.cid;
            let gmsg_id_list=valObj.gmsg_id_list;
            if (gmsg_id_list && cid && state[cid]){
                let chatMessageList=state[cid].chatMessageList;
                for(var i in gmsg_id_list){
                    let gmsg_id = gmsg_id_list[i];
                    for(let j=chatMessageList.length-1; j>=0; j--){
                        if (gmsg_id==chatMessageList[j].gmsg_id){
                            chatMessageList.splice(j,1);
                            // 如果存在引用消息，并且引用消息也在本次撤回列表中，则一并更新引用消息的状态
                            if (chatMessageList[j].quote_message && gmsg_id_list.includes(chatMessageList[j].quote_message.gmsg_id)) {
                                Vue.set(chatMessageList[j].quote_message, 'been_withdrawn', 1);
                            }
                        }
                        if(chatMessageList[j].quote_message && chatMessageList[j].quote_message.gmsg_id==gmsg_id){
                            Vue.set(chatMessageList[j].quote_message,'been_withdrawn',1);
                        }
                    }
                }
            }
        },
        withDrawChatMessagesByGmsgIdList(state,valObj){
            //撤回消息
            let cid=valObj.cid;
            let gmsg_id_list=valObj.gmsg_id_list;
            if (gmsg_id_list && cid && state[cid]){
                let chatMessageList=state[cid].chatMessageList;
                for(var i in gmsg_id_list){
                    let gmsg_id = gmsg_id_list[i];
                    for(let j=chatMessageList.length-1; j>=0; j--){
                        if (gmsg_id==chatMessageList[j].gmsg_id){
                            if(chatMessageList[j].msg_type === window.vm.$store.state.systemConfig.msg_type.COMMENT){
                                window.vm.$store.commit('gallery/deleteCommentByCommentId',{
                                    resource_id:chatMessageList[j].resource_id,
                                    comment_id:chatMessageList[j].comment_id
                                });
                            }
                            Vue.set(chatMessageList[j],'msg_type',20);
                            Vue.set(chatMessageList[j],'been_withdrawn',2);
                            // 如果存在引用消息，并且引用消息也在本次撤回列表中，则一并更新引用消息的状态
                            if (chatMessageList[j].quote_message && gmsg_id_list.includes(chatMessageList[j].quote_message.gmsg_id)) {
                                Vue.set(chatMessageList[j].quote_message, 'been_withdrawn', 2);
                            }
                            window.vm.$store.commit('chatList/setLastMessage',{cid:valObj.cid,message:chatMessageList[j]});
                            // window.mainDB.conversationMessageList.put(chatMessageList[j])
                        }
                        if(chatMessageList[j].quote_message && chatMessageList[j].quote_message.gmsg_id==gmsg_id){
                            Vue.set(chatMessageList[j].quote_message,'been_withdrawn',2);
                        }

                    }
                }
            }
        },
        sendAck(state,data){
            //消息确认送达
            console.log('sendAck',data)
            var chatMessageList=state[data.cid].chatMessageList
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]

                if (message.tmp_gmsg_id==data.message.tmp_gmsg_id) {
                    clearTimeout(message.sendingTimer)
                    data.message.sending=false
                    data.message.sendFail=false
                    data.message.timeout=message.timeout
                    data.message.nickname=data.message.nickname||message.nickname;
                    data.message.loaded=false;
                    data.message.avatar=data.message.avatar||message.avatar
                    data.message.avatar_local=data.message.avatar_local||message.avatar_local
                    clearTimeout(data.message.timeout)
                    chatMessageList.splice(i,1,data.message)
                    if (data.message.origin_gmsg_id) {
                        //发送检查图片时聚合
                        chatMessageList.splice(i,1)
                        this.commit('conversationList/setChatMessage',{
                            list:[data.message],
                            cid:data.cid,
                            type:'replace',
                        })
                    }else{
                        chatMessageList.splice(i,1,data.message)
                    }
                    break;
                }
            }
        },
        setSendFail(state,data){
            //消息发送失败
            var chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.tmp_gmsg_id==data.tmp_gmsg_id) {
                    message.sending=false
                    message.sendFail=true
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }

        },
        setCommentToConversation(state,data){
            //已废弃
            let gallery_list=state[data.cid].galleryObj.gallery_list
            for(let commentKey in data.list){
                for(let i=0;i<gallery_list.length;i++){
                    let item=gallery_list[i]
                    if(commentKey==item.resource_id){
                        item.comment_list=data.list[commentKey].comment_list
                        item.tag_names=data.list[commentKey].tag_names
                        item.tags_list=data.list[commentKey].tags_list
                        //强制视图更新
                        gallery_list.splice(i,1,item)
                        break;
                    }
                }
            }
        },
        updateMessageImageLocalUrl(state,data){
            //更新聊天消息图像本地地址
            let index=data.index;
            let gmsg_id=data.gmsg_id;
            let cid=data.cid;
            let list=state[cid].chatMessageList

            if (gmsg_id) {
                for (let i=0;i<list.length;i++) {
                    let item=list[i];
                    if (item && item.gmsg_id==gmsg_id) {
                        index=i;
                        break;
                    }
                }
            }

            let item=list[index]
            if (item) {
                item.url_local=data.url_local
                list.splice(index,1,item)
                // window.mainDB.conversationMessageList.put(item)
            }
        },
        updateAnalyzeImageLocalUrl(state,data){
            //更新AI分析图像本地地址
            let index=data.index.split("_")[1];
            let j_index=data.index.split("_")[2];
            let gmsg_id=data.gmsg_id;
            let cid=data.cid;
            let list=state[cid].chatMessageList

            if (gmsg_id) {
                for (let i=0;i<list.length;i++) {
                    let item=list[i];
                    if (item && item.gmsg_id==gmsg_id) {
                        index=i;
                        break;
                    }
                }
            }

            let item=list[index]
            if (item) {
                item.ai_analyze.messages[j_index].url_local=data.url_local
                list.splice(index,1,item)
                // window.mainDB.conversationMessageList.put(item)
            }
        },
        updateMessageAvatarLocalUrl(state,data){
            //更新聊天消息头像本地地址
            let index=data.index;
            let gmsg_id=data.gmsg_id;
            let cid=data.cid;
            let list=state[cid].chatMessageList

            if (gmsg_id) {
                for (let key in list) {
                    let item=list[key];
                    if (item && item.gmsg_id==gmsg_id) {
                        index=key;
                        break;
                    }
                }
            }

            let item=list[index]
            if (item) {
                item.avatar_local=data.avatar_local
                list.splice(index,1,item)
                // window.mainDB.conversationMessageList.put(item)
            }
        },
        updateMessageAudioLocalUrl(state,data){
            //更新聊天消息录音文件本地地址
            let index=data.index;
            let cid=data.cid;
            let list=state[cid].chatMessageList
            let item=list[index]
            item.url=data.url
            list.splice(index,1,item)
            // window.mainDB.conversationMessageList.put(item)
        },
        updateFileProgress(state,data){
            //更新文件上传进度
            let msg=data.msg;
            let chatMessageList=state[msg.group_id].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.file_id==msg.file_id) {
                    if(data.hasOwnProperty('percent')){
                        message.percent=data.percent
                    }
                    if(data.hasOwnProperty('uploadId')){
                        message.uploadId = data.uploadId
                    }
                    if(data.hasOwnProperty('pauseUpload')){
                        message.pauseUpload = data.pauseUpload
                    }
                    if(data.hasOwnProperty('uploadError')){
                        message.uploadFail = data.uploadError
                    }
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        setMessageSending(state,data){
            let chatMessageList=state[data.cid].chatMessageList
            let message=chatMessageList[data.index]
            message.sendingTimer=setTimeout(()=>{
                message.sending=true;
            },500)
            message.uploading=false;
            chatMessageList.splice(data.index,1,message)
        },
        updateUploadFail(state,data){
            //设置文件上传失败
            let chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.file_id==data.file_id) {
                    message.uploadFail=true;
                    message.uploading=false;
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        updateGroupSettingLocalUrl(state,data){
            //更新群设置页会诊文件本地地址
            let imgObj=data.imgObj;
            let cid=data.cid;
            let list=state[cid].galleryObj.gallery_list
            for(let index=0;index<list.length;index++){
                let item=list[index]
                if (item.gmsg_id==imgObj.gmsg_id) {
                    item.url_local=data.url_local
                    list.splice(index,1,item)
                }
            }
        },
        updateFriendToAttendeeList(state,data){
            //更新所有会话好友信息
            let key='attendee_'+data.id
            for(let item in state){
                let conversation=state[item]
                if(!conversation){
                    continue
                }
                let friend=conversation.attendeeList[key]
                if (friend) {
                    friend.avatar=data.avatar
                    friend.avatar_local=data.avatar_local
                    friend.state=data.state
                    friend.nickname=data.nickname
                    friend.hospital_id=data.hospital_id
                    Vue.set(conversation.attendeeList,key,friend);
                    //更新聊天消息的头像昵称
                    for(let i=0;i<conversation.chatMessageList.length;i++){
                        let message=conversation.chatMessageList[i]
                        if (message.sender_id==data.id) {
                            message.avatar=data.avatar;
                            message.avatar_local=data.avatar_local;
                            message.nickname=data.nickname;
                            conversation.chatMessageList.splice(i,1,message)
                            // window.mainDB.conversationMessageList.put(message)
                        }
                    }
                }
            }
        },
        pushMoreGroupFile(state,data){
            //群文件加载更多
            let galleryObj=state[data.cid].galleryObj;
            Object.keys(data).forEach(key=>{
                if(key.indexOf('index')>-1){
                    galleryObj[key] = data[key]
                }else if(key.indexOf('list')>-1){
                    galleryObj[key]=galleryObj[key].concat(data[key])
                }
            })
        },
        updateIsPlayingVideo(state,valObj){
            let cid=valObj.cid;
            Vue.set(state[cid],"is_playing_video",valObj.is_playing_video);
        },
        updateAttendeeLocalUrl(state,data){
            //更新群成员头像本地地址
            let key='attendee_'+data.uid
            let conversation=state[data.cid]
            let friend=conversation.attendeeList[key]
            if (friend) {
                friend.avatar_local=data.avatar_local
                Vue.set(conversation.attendeeList,key,friend)
            }
        },
        addAttendee(state,data){
            //添加群成员
            let cid=data.groupid;
            let key='attendee_'+data.userid;
            let conversation=state[cid];
            if(!conversation){
                return
            }
            Vue.set(conversation.attendeeList,key,data)
        },
        deleteAttendee(state, data){
            //删除群成员
            let cid = data.cid;
            let key='attendee_'+data.uid;
            let conversation=state[cid];
            let attendee=conversation.attendeeList[key];
            if(attendee){
                attendee.attendeeState = 0;
                Vue.set(conversation.attendeeList,key,attendee);
            }
        },
        updateAllAttendeeTempToFormal(state, data){
            let cid = data.cid;
            let conversation=state[cid];

            for(let key in conversation.attendeeList){
                let attendee=conversation.attendeeList[key];
                if(attendee){
                    if(2 == attendee.attendeeState || 3 == attendee.attendeeState){
                        attendee.attendeeState = 1;
                        Vue.set(conversation.attendeeList,key,attendee);
                    }
                }
            }
        },
        updateAttendeeTempToFormal(state, data){
            let cid = data.cid;
            let conversation=state[cid];
            let key='attendee_'+data.uid;

            let attendee=conversation.attendeeList[key];
            if(attendee){
                if(2 == attendee.attendeeState || 3 == attendee.attendeeState){
                    attendee.attendeeState = 1;
                    Vue.set(conversation.attendeeList,key,attendee);
                }
            }
        },
        updateAttendeeState(state, data){
            //更新群成员
            let cid = data.cid;
            let key='attendee_'+data.uid;
            let attendeeState = data.attendeeState
            let conversation=state[cid];
            let attendee=conversation.attendeeList[key];
            if(attendee){
                attendee.attendeeState = attendeeState;
                Vue.set(conversation.attendeeList,key,attendee);
            }

        },
        updateAttendeeRole(state, data){
            let cid = data.cid;
            let conversation=state[cid];
            for(let key in data.attendeeList){
                let attendee=conversation.attendeeList[key];
                if(attendee){
                    attendee.role = data.attendeeList[key].role;
                    Vue.set(conversation.attendeeList,key,attendee);
                }
            }

            // 同步更新权限控制中的角色信息
            if (permissionManager && permissionManager.isInitialized()) {
                try {
                    // 遍历更新的参与者列表，同步更新权限管理器中的角色信息
                    for(let key in data.attendeeList){
                        const attendeeData = data.attendeeList[key];
                        if (attendeeData && (attendeeData.uid || attendeeData.userid)) {
                            permissionManager.updateUserRoleFromAttendeeData(cid, attendeeData);
                        }
                    }
                    console.log('移动端 Store已同步更新权限管理器中的会话角色信息:', {
                        conversationId: cid,
                        updatedAttendees: Object.keys(data.attendeeList)
                    });
                } catch (error) {
                    console.error('移动端 Store同步权限管理器会话角色信息失败:', error);
                }
            }
        },
        updateAttendeeAliasName(state, data){
            let cid = data.cid;
            let conversation=state[cid];
            let key='attendee_'+data.uid;

            let attendee=conversation.attendeeList[key];
            if(attendee){
                attendee.alias_name = data.aliasName
                Vue.set(conversation.attendeeList,key,attendee);
            }
        },
        updateSubjectToConversation(state,data){
            //更新群聊名称
            let cid=data.cid;
            let subject=data.subject;
            Vue.set(state[cid],"subject",subject);
        },
        updateIsPublic(state,data){
            //更新群公开属性
            let cid=data.cid;
            let is_public=data.is_public;
            Vue.set(state[cid],"is_public",is_public);
        },
        updateIsLiveRecord(state,data){
            //更新群公开属性
            let cid=data.cid;
            let record_mode=data.record_mode;
            Vue.set(state[cid],"record_mode", record_mode);
        },
        updateGroupOwnerId(state,data){
            //更新群主
            Vue.set(state[data.cid],"creator_id",data.user_id);
        },
        updataVoiceCtrlMode(state,data){
            //更新语音模式
            let cid=data.cid;
            let voice_ctrl_mode=data.voice_ctrl_mode;
            Vue.set(state[cid],"voice_ctrl_mode", voice_ctrl_mode);
        },
        updataMuteCtrlMode(state,data){
            //更新静音模式
            let cid=data.cid;
            let mute_ctrl_mode=data.mute_ctrl_mode;
            Vue.set(state[cid],"mute_ctrl_mode", mute_ctrl_mode);
        },
        updateProgressByImgId(state,data){
            //更新文件上传进度
            let percent=data.percent||100
            let chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.file_id==data.file_id) {
                    message.percent=percent
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        updateFriendToConversationList(state,data){
            for(let key in state){
                let conversation=state[key];
                if(!conversation){
                    continue
                }
                if(conversation&&conversation.is_single_chat&&conversation.fid==data.id){
                    conversation.subject=data.nickname;
                }

                let attendee='attendee_' + data.id;
                let user=conversation.attendeeList[attendee]
                if(user && (user.hospital_id != data.hospital_id)){
                    user.hospital_id  = data.hospital_id;
                }
            }
        },
        updateViewMode(state,data){
            Vue.set(state[data.cid],"view_mode",data.value);
        },
        deleteConversationList(state, data){
            console.error('deleteConversationList')
            if(state[data.cid]){
                delete state[data.cid];
                // window.mainDB.conversationList.delete(data.cid)
            }
        },
        // updateAiAnalyzeReport(state, data){
        //     //更新AI分析结果
        //     let cid = data.cid||data.group_id
        //     let chatMessageList=state[data.cid].chatMessageList
        //     for(let i=chatMessageList.length-1;i>=0;i--){
        //         //从聊天记录尾部遍历，提高效率
        //         let message=chatMessageList[i]
        //         if (message.ai_analyze_id==data.ai_analyze_id) {
        //             message.ai_analyze.report=data.report;
        //             chatMessageList.splice(i,1,message)
        //             break;
        //         }
        //     }
        // },

        updateAiAnalyzeReport(state, data){
            //更新AI分析结果
            let chatMessageList=state[data.cid].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=cloneDeep(chatMessageList[i])
                let ai_analyze_ids = (message.ai_analyze_list||[]).reduce((h,v)=>{
                    h.push(v.id);return h;
                },[])

                if (message.ai_analyze_id==data.ai_analyze_id||ai_analyze_ids.indexOf(data.ai_analyze_id)>=0) {
                    message.ai_analyze.report=data.report;
                    message.ai_analyze.status = 1
                    message.loaded=false;//可以从新加载图片，描绘描迹
                    message.ai_analyze_list = message.ai_analyze_list||[]
                    if(ai_analyze_ids.indexOf(data.ai_analyze_id)>=0){
                        let ai_analyze_list = (message.ai_analyze_list).reduce((h,v)=>{
                            if(data.ai_analyze_id==v.id){
                                v=cloneDeep({...v,...data,status:1,id:data.ai_analyze_id})
                            }
                            h.push(v);
                            return h;
                        },[])
                        message.ai_analyze_list = ai_analyze_list
                    }else{
                        message.ai_analyze_list = cloneDeep([...message.ai_analyze_list,{...data,status:1,id:data.ai_analyze_id}])
                    }
                    message.ai_analyze_list  = cloneDeep(uniqBy(message.ai_analyze_list, 'id'))
                    message.ai_analyze_report={...data,...data.report}

                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        deleteMessageList(state,cid){
            console.error('deleteMessageList')
            let conversation=state[cid]
            if (conversation) {
                conversation.chatMessageList=[];
            }
        },
        updateMessageList(state,{cid,list}){
            let conversation=state[cid]
            if (conversation) {
                Vue.set(conversation,'chatMessageList',list)
            }
        },
        updateHlsVideo(state,data){
            Vue.set(state[data.cid],"hls_ultrasound_video",data.hls_ultrasound_video);
            Vue.set(state[data.cid],"hls_gesture_video",data.hls_gesture_video);
        },
        updateAnnounce(state,data){
            //更新群说明
            let cid=data.cid;
            Vue.set(state[cid],"announcement",data);
        },
        updateStartVoiceMsg(state,data){
            var chatMessageList=state[data.cid].chatMessageList
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.sending) {
                    message.sending=false
                    chatMessageList.splice(i,1,message)
                    break;
                }
            }
        },
        deleteStartVoiceMsg(state,data){
            var chatMessageList=state[data.cid].chatMessageList
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.sending) {
                    chatMessageList.splice(i,1)
                    break;
                }
            }
        },
        addConferencePlan(state, data){
            let cid=data.group_id;
            let conference_id = data.conference_id;
            Vue.set(state[cid].conferencePlanList, conference_id, data)
        },
        delConferencePlan(state, data){
            let cid=data.group_id;
            let conference_id = data.conference_id;
            let conferencePlanList = state[cid].conferencePlanList;
            if(conferencePlanList){
                // delete conferencePlanList[conference_id];
                Vue.delete(state[cid].conferencePlanList,conference_id)
            }
        },
        updateConversationImage(state,data){
            let msg=data;
            let galleryList=state[msg.group_id].galleryObj.gallery_list
            for(let i=galleryList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=galleryList[i]
                if (message.gmsg_id==msg.gmsg_id) {
                    for(let key in msg){
                        message[key]=msg[key]
                    }
                    galleryList.splice(i,1,message)
                    break;
                }
            }
        },
        updateMuteToConversation(state,data){
            let preferences=state[data.cid]&&state[data.cid].preferences;
            if (preferences) {
                preferences.is_mute=data.is_mute;
                Vue.set(state[data.cid],'preferences',preferences)
            }
        },
        updateIworksList(state,data){
            Vue.set(state[data.cid],'iworksList',{
                index:data.index,
                list:data.list
            })
        },
        updateResourceDes(state,data){
            let gallery_list=state[data.cid].galleryObj.gallery_list;
            for(let i=0;i<gallery_list.length;i++){
                let item=gallery_list[i]
                if(data.resource_id == item.resource_id){
                    item.des = data.des;
                    //强制视图更新
                    gallery_list.splice(i,1,item)
                    break;
                }
            }

            let gallery_list_2=state[data.cid].chatMessageList;
            for(let i=0;i<gallery_list_2.length;i++){
                let item=gallery_list_2[i]
                if(data.resource_id == item.resource_id){
                    item.des = data.des;
                    //强制视图更新
                    gallery_list_2.splice(i,1,item)
                    break;
                }
            }
        },
        updateConversationAvatar(state, data){
            const cid = data.cid
            const conversation = state[cid]
            if(conversation){
                for(const key in data) {
                    conversation[key] = data[key]
                }
            }
        },
        updateAttendeeVideoStatus(state,data){
            //更新成员的视频状态
            let key='attendee_'+data.uid
            let conversation=state[data.cid]
            let friend=conversation.attendeeList[key]
            if (friend) {
                friend.video_status=data.video_status
                Vue.set(conversation.attendeeList,key,friend)
            }
        },
        updateDeviceList(state,data){
            if(data&&data.list){
                let conversation=state[data.cid]
                if(conversation){
                    conversation['device_list']=data.list;
                }
            }

        },
        addDeviceToConversation(state,data){
            let conversation=state[data.cid]
            if (conversation&&conversation.device_list) {
                if(!conversation.device_list.find(item=>(item.device_id===data.device.device_id))){
                    conversation.device_list.push(data.device)
                }
            }
        },
        updateChatMessage(state,data){
            let msg=data;
            let chatMessageList=state[msg.group_id].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.gmsg_id==msg.gmsg_id) {
                    for(let key in msg){
                        message[key]=msg[key]
                    }
                    chatMessageList.splice(i,1,message)
                    // window.mainDB.conversationMessageList.put(message)
                    break;
                }
            }
        },
        updateChatMessageLiveRecordData(state,data){
            let msg=data;
            if(!state[msg.group_id]){
                return
            }
            let chatMessageList=state[msg.group_id].chatMessageList
            for(let i=chatMessageList.length-1;i>=0;i--){
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.resource_id==msg.resource_id) {
                    Vue.set(state[msg.group_id].chatMessageList[i],'live_record_data',data.live_record_data)
                    Vue.set(state[msg.group_id].chatMessageList[i],'coverUrl',data.coverUrl)
                    // window.mainDB.conversationMessageList.put(message)
                    break;
                }
            }

            let gallery_list=state[msg.group_id].galleryObj.gallery_list; //全部文件
            for(let i=0;i<gallery_list.length;i++){
                let item=gallery_list[i]
                if(data.resource_id == item.resource_id){
                    for(let key in msg){
                        item[key]=msg[key]
                    }
                    //强制视图更新
                    gallery_list.splice(i,1,item)
                    break;
                }
            }
            let video_list=state[msg.group_id].galleryObj.video_list; //视频文件
            for(let i=0;i<video_list.length;i++){
                let item=video_list[i]
                if(data.resource_id == item.resource_id){
                    for(let key in msg){
                        item[key]=msg[key]
                    }
                    //强制视图更新
                    video_list.splice(i,1,item)
                    break;
                }
            }
        },
        updateMessageListIsLoaded(state,{is_loaded_history_list,cid}){
            if(cid&&state[cid]){
                Vue.set(state[cid],"is_loaded_history_list",is_loaded_history_list);
            }
        },
        updateMessageListNeedReload(state,{is_need_reload,cid}){
            if(cid&&state[cid]){
                Vue.set(state[cid],"is_need_reload",is_need_reload);
            }
        },
        updateConversation(state,data){
            let cid=data.cid;
            Vue.set(state[cid],data.key, data.value);
        },
        clearAiTaskInfo(state,data){
            let {task_id,list,cid} = data
            let aiTaskInfo = state[cid].aiTaskInfo
            if(aiTaskInfo && aiTaskInfo[task_id]){
                Vue.set(state[cid].aiTaskInfo,task_id, {})
                delete state[cid].aiTaskInfo[task_id]
            }else{
                // Vue.set(state[cid].aiTaskInfo,task_id, {})
                // delete state[cid].aiTaskInfo[task_id]
            }
        },
        updateAiTaskInfo(state,data){
            let {task_id,list,cid} = data
            let aiTaskInfo = state[cid].aiTaskInfo
            if(list&&aiTaskInfo && aiTaskInfo[task_id]){
                for(let item of list){
                    let clip_id = item.clip_id
                    let new_item = cloneDeep(item)
                    if(clip_id && aiTaskInfo[task_id][clip_id] ){
                        let old_item = aiTaskInfo[task_id][clip_id][0]
                        new_item  = item.score>old_item.score ? new_item : aiTaskInfo[task_id][clip_id][0]
                        Vue.set(state[cid].aiTaskInfo[task_id],clip_id,[new_item])
                    }else{
                        Vue.set(state[cid].aiTaskInfo[task_id],clip_id,[new_item])
                    }
                }
            }else{
                Vue.set(state[cid].aiTaskInfo,task_id, groupBy(list,'clip_id'))
            }

        }
    },
    actions: {},
    getters: {}
}
